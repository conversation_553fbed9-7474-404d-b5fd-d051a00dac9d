## openjianghu01

## window 配置 git bash

windows环境 需要配置TREMINAL为 `git bash`
![git-bash](/.vscode/img/git-bash.png)

## 批量替换 mysql信息

- `port:.*,` ---> `port: 43301,`
- `password:.*,` ---> `password: 'xxx',`

![replace-knex](/.vscode/img/replace-knex.png)

## 项目端口

> ==后面再统一更新==
- 项目端口: 7101 ---> 101.openjianghu-admin 以次类推


## 本地运行项目

- 使用VScode打开 **dev02**
- 使用VScode的`launch.json`工具进行 批量 操作项目
    1. 拉取项目
    2. copy config 或者 copy config.env
    3. npm install
- 使用`launch.json`中的按钮启动项目;  例如: 201.jianghujs-1table-crud 


## openjianghu01项目链接汇总

> 注意：106.cn-openjianghu-seo-v2 需要 切到 feature-openSource-doc 分支运行
- 101.openjianghu-admin          http://127.0.0.1:8306     database: openjianghu_admin         https://en.jianghujs.org          
- 102.openjianghu-seo            http://127.0.0.1:8307     database: openjianghu_seo           https://en.jianghujs.org/admin                    
- 103.cn-openjianghu-admin       http://127.0.0.1:9306     database: cn_openjianghu_admin      https://cn.jianghujs.org          
- 104.cn-openjianghu-seo         http://127.0.0.1:9307     database: cn_openjianghu_seo        https://cn.jianghujs.org/admin     
- 105.cn-openjianghu-admin-v2    http://127.0.0.1:9406     database: cn_openjianghu_admin_v2   https://cn.openjianghu.org 
- 106.cn-openjianghu-seo-v2      http://127.0.0.1:9407     database: cn_openjianghu_seo_v2     https://cn.openjianghu.org/admin

## 问题

```bash
- https://www.jianghujs.org         https://www.jianghujs.org/admin        
- https://www.openjianghu.org       https://www.openjianghu.org/admin   

nginx上配置 这两个域名 都 访问到 8306/8307, 但实际上却访问到 9306/9307
而且 www.openjianghu.org的证书过期了

猜测：因为jhpanle 的nginx上 只解析了 jianghujs.org  没有解析 www.jianghujs.org  
```

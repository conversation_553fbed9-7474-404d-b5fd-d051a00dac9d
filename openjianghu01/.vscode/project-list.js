const projectList = [
    { id: '001', name: 'jianghu',                       url: '**************:jianghujs/jianghu.git',              },
    { id: '002', name: 'jianghu-init',                  url: '**************:jianghujs/jianghu-init.git',              },
    { id: '003', name: 'jianghu-studio',                url: '**************:jianghujs/jianghu-studio.git',              },
    { id: '004', name: 'jianghu-duoxing',               url: '*************************:duoxing/jianghu-duoxing.git',              },
    { id: '005', name: 'jh-panel',                      url: '**************:jianghujs/jh-panel.git',              },
        
    { id: '014.java', name: 'jianghu-duoxing',           url: '*************************:jianghuJava/jianghu-duoxing.git',              },
    { id: '011.java', name: 'jianghu-parent',            url: '*************************:jianghuJava/jianghu-parent.git',              },
    { id: '012.java', name: 'jianghu-starter',           url: '*************************:jianghuJava/jianghu-starter.git',              },
    { id: '205.java', name: 'jianghu-basic',             url: '*************************:jianghuJava/jianghu-basic.git',              },
    { id: '220.java', name: 'jianghu-advanced',          url: '*************************:jianghuJava/jianghu-advanced.git',              },
    { id: '309.java', name: 'jianghu-bi',                url: '*************************:jianghuJava/jianghu-bi.git',              },

    { id: 'JS.121', name: "jianghu-init-json-test",           url: "**************:jianghujs/jianghu-init-json-test.git"}, // dev02数据库
    { id: 'JS.205', name: 'jianghujs-basic',                   url: '**************:jianghujs/jianghujs-basic',                   isPublic: true, },
    { id: 'JS.220', name: 'jianghujs-advanced',                url: '**************:jianghujs/jianghujs-advanced',                isPublic: true, },
    { id: 'JS.303', name: 'jianghu-hr',                   database: 'jianghu_hr',        url: '**************:jianghujs/jianghu-hr.git',              },

]

projectList.forEach(project => {
    project.name = project.id + '.' + project.name
})

module.exports = projectList
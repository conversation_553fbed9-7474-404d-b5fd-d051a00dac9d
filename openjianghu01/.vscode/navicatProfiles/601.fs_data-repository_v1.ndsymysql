{"Version": 1.1, "General": {"ContinueOnError": false, "IncludeDelete": true, "IncludeInsert": true, "IncludeUpdate": true, "RunMultipleSQL": false, "ShowDetails": true, "SourceCatalog": "", "SourceCloudInstanceName": "", "SourceProject": "", "SourceProjectOwnerNavicatID": "", "SourceSchema": "fs_data_repository", "SourceServer": "foison06", "SourceServerType": "MYSQL", "TargetCatalog": "", "TargetCloudInstanceName": "", "TargetProject": "", "TargetProjectOwnerNavicatID": "", "TargetSchema": "fs_data_repository", "TargetServer": "foisonProd", "TargetServerType": "MYSQL", "UseTransaction": false}, "Tables": [{"Source": {"Name": "_constant", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "_constant", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "_page", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "_page", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "_resource", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "_resource", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "_table_sync_config", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "_table_sync_config", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "_test_case", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "_test_case", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}, {"Source": {"Name": "_ui", "ObjectType": "Table_MYSQL"}, "Target": {"Name": "_ui", "ObjectType": "Table_MYSQL"}, "Keys": [], "Fields": []}]}
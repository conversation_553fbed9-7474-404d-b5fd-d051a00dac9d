{"Version": 1.4, "General": {"CompareAutoIncValue": false, "CompareCharacterSet": true, "CompareChecks": true, "CompareDefiner": false, "CompareEvents": false, "CompareExcludes": true, "CompareFKeys": true, "CompareIndexes": true, "ComparePKeys": true, "ComparePartitions": false, "CompareRules": true, "CompareSequenceLastValues": true, "CompareSequences": true, "CompareStoredProcs": false, "CompareTableOptions": true, "CompareTables": true, "CompareTablespacePhyAttrs": false, "CompareTriggers": false, "CompareUniques": true, "CompareViews": true, "ContinueOnError": false, "DropCascade": false, "IdentifierCaseSensitivity": "default", "SourceCatalog": "", "SourceCloudInstanceName": "", "SourceProject": "", "SourceProjectOwnerNavicatID": "", "SourceSchema": "fs_data_repository", "SourceServer": "foison06", "SourceServerType": "MYSQL", "TargetCatalog": "", "TargetCloudInstanceName": "", "TargetProject": "", "TargetProjectOwnerNavicatID": "", "TargetSchema": "fs_data_repository", "TargetServer": "foisonProd", "TargetServerType": "MYSQL"}}
const fs = require('fs');
const path = require('path');

(async () => {
    const projects = fs.readdirSync("./foison06");
    const list = [];
    let startId = 600;
    for (const projectName of projects) {
        const gitConfigFilePath = `/Users/<USER>/VsCodeProjects/jh/github-repo/jianghujs-script-util/foison06/${projectName}/.git/config`;
        try {
            const gitConfig = await fs.promises.readFile(gitConfigFilePath, 'utf8');
            const repoUrl = gitConfig.match(/url = (.*)/)[1];
            list.push({ 
                id: startId, 
                name: startId + '.' + projectName,
                url: repoUrl
            });

            startId++;
        } catch (error) {
            console.log(error, "skip")
        }
    }

    console.log(JSON.stringify(list, null, 4));
})();


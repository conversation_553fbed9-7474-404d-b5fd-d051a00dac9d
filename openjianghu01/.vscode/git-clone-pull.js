const fs = require('fs');
const { exec } = require('child_process');

let retryCommand = '';
let commandTaskErrorList = [];
async function cloneOrUpdateProjects() {
    // 导入 project-list.js 文件
    const projectList = require('./project-list.js');

    // 遍历项目列表
    const commandTaskList = [];
    for (let i = 0; i < projectList.length; i++) {
        const project = projectList[i];
        const { name, url } = project;

        // 执行 git clone 或 git pull 命令
        let gitCommand = `cd ${__dirname + '/..'} && git clone ${url} ${name}`;
        if (fs.existsSync(__dirname + '/../' + name)) {
            console.log(`${name} 已存在, 尝试执行 git pull`);
            gitCommand = `cd ${__dirname + '/../' + name} && git pull`;
        }
        commandTaskList.push(executeCommand(gitCommand, name))
    }
    await Promise.all(commandTaskList)
    if (commandTaskErrorList.length > 0) {
        console.log('=====================================================');
        console.log(`${commandTaskErrorList.length}个执行异常, 开始第1次重试`);
        console.log(retryCommand);
        const commandTaskErrorListTemp = [...commandTaskErrorList];
        retryCommand='';
        commandTaskErrorList = [];
        await Promise.all(commandTaskErrorListTemp);
    }
    if (commandTaskErrorList.length > 0) {
        console.log('=====================================================');
        console.log(`${commandTaskErrorList.length}个执行异常, 开始第2次重试`);
        console.log(retryCommand);
        const commandTaskErrorListTemp = [...commandTaskErrorList];
        retryCommand='';
        commandTaskErrorList = [];
        await Promise.all(commandTaskErrorListTemp);
    }
    if (commandTaskErrorList.length > 0) {
        console.log('=====================================================');
        console.log(`${commandTaskErrorList.length}个执行异常, 开始第3次重试`);
        console.log(retryCommand);
        const commandTaskErrorListTemp = [...commandTaskErrorList];
        retryCommand='';
        commandTaskErrorList = [];
        await Promise.all(commandTaskErrorListTemp);
    }
    console.log('=====================================================');
    console.log('结束');
    setTimeout(() => {process.exit(0);}, 2000);
}

function executeCommand(command, projectName) {
    return new Promise((resolve, reject) => {
        const startTime = new Date().getTime();
        let isFinished = false;
        setTimeout(() => {
            if (!isFinished) {
                retryCommand += `${command}\n`;
                commandTaskErrorList.push(executeCommand(command, projectName));
                console.error(`更新 ${projectName} 20s超时, 可尝试手动执行命令 ${command}`);
                resolve();
            }
        }, 20000);
        exec(command, (error, stdout, stderr) => {
            const endTime = new Date().getTime();
            const useTime = (endTime - startTime)/1000.0;
            if (error) {
                commandTaskErrorList.push(executeCommand(command, projectName));
                console.error(`更新 ${projectName} 出错: ${error.message}, 可尝试手动执行命令 ${command}, 用时: ${useTime}/s`);
                retryCommand += `${command}\n`;
                isFinished=true;
                resolve();
                return;
            }
            let changedOutput = "";
            const changedMatch = stdout.match(/^(.*files changed.*)$/m);
            if (changedMatch) {
                changedOutput = changedMatch[1];
            }
            // files changed
            console.log(`成功更新 ${projectName}, 用时: ${useTime}/s`, changedOutput);
            isFinished=true;
            resolve();
        });
    });
}

cloneOrUpdateProjects();

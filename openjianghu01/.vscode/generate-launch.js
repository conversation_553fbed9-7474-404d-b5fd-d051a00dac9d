// 用于生成 launch.json 的脚本
const projectList = require('./project-list.js');

for (const project of projectList) {
    const { subProjectList } = project;
    if (subProjectList) {
        for (const subProject of subProjectList) {
            const { subdir } = subProject;
            console.log(`{ "name": "${project.name}/${subdir}", "command": "npm --prefix=${project.name}/${subdir} run dev", "request": "launch", "type": "node-terminal" },`)
        }
    } 
    console.log(`{ "name": "${project.name}", "command": "npm --prefix=${project.name} run dev", "request": "launch", "type": "node-terminal" },`)
}
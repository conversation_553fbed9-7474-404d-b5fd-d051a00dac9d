const fs = require('fs');
const { exec } = require('child_process');

async function npmInstall() {
    try {
        // 导入 project-list.js 文件
        const projectList = require('./project-list.js');

        // 遍历项目列表
        for (const project of projectList) {
            const { name, url, subProjectList } = project;

            if (!fs.existsSync(__dirname + '/../' + name)) {
                console.error(`${name} 未拉取, 请执行项目拉取命令，或手动执行 cd ${__dirname + '/..'} && git clone ${url} ${name}`);
                continue
            }
            if (subProjectList) {
                console.log(`${name} 为多应用项目，在子应用目录下执行 npm run stop`);
                for (const subProject of subProjectList) {
                    const { subdir } = subProject;
                    await executeCommand(`cd ${__dirname + '/../' + name + '/' + subdir} && ${subProject.stopScript || 'npm run stop'}`, name + '/' + subdir);
                }
            } else {
                await executeCommand(`cd ${__dirname + '/../' + name} && ${project.stopScript || 'npm run stop'}`, name);
            }
        }
    } catch (error) {
        console.error('发生错误:', error);
    }
}

function executeCommand(command, projectName) {
    return new Promise((resolve, reject) => {
        const startTime = new Date().getTime();
        exec(command, (error, stdout, stderr) => {
            const endTime = new Date().getTime();
            const useTime = (endTime - startTime)/1000.0;
            if (error) {
                console.error(`出错: ${projectName}, ${error.message}, 可尝试手动执行命令 ${command}, 用时: ${useTime}/s`);
                resolve();
                return;
            }
            console.log(`成功: ${projectName}, 用时: ${useTime}/s`);
            resolve();
        });
    });
}

npmInstall();

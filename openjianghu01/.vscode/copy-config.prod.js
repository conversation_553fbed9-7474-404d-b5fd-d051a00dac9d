const fs = require('fs');
const { exec } = require('child_process');

async function npmInstall() {
    try {
        // 导入 project-list.js 文件
        const projectList = require('./project-list.js');

        // 遍历项目列表
        for (const project of projectList) {
            const { name, url, subProjectList } = project;

            if (!fs.existsSync(__dirname + '/../' + name)) {
                console.error(`${name} 未拉取, 请执行项目拉取命令，或手动执行 cd ${__dirname + '/..'} && git clone ${url} ${name}`);
                continue
            }
            if (subProjectList) {
                console.log(`${name} 为多应用项目，在子应用目录下执行 copy`);
                for (const subProject of subProjectList) {
                    const { subdir } = subProject;
                    if (fs.existsSync(__dirname + '/../' + name + '/' + subdir + '/config/config.prod.js')) {
                        console.log(`${name + '/' + subdir} 项目下存在 config/config.prod.js 文件，跳过`)
                        continue
                    }
                    await executeCommand(`cd ${__dirname + '/../' + name + '/' + subdir} && cp config/config.prod.example.js config/config.prod.js`, name + '/' + subdir);
                }
            } else {
                if (fs.existsSync(__dirname + '/../' + name + '/config/config.prod.js')) {
                    console.log(`${name} 项目下存在 config/config.prod.js 文件，跳过`)
                    continue
                }
                await executeCommand(`cd ${__dirname + '/../' + name} && cp config/config.prod.example.js config/config.prod.js`, name);
            }
        }
    } catch (error) {
        console.error('发生错误:', error);
    }
}

function executeCommand(command, projectName) {
    return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`出错: ${projectName}, ${error.message}, 可尝试手动执行命令 ${command}`);
                resolve();
                return;
            }
            console.log(`成功: ${projectName}`);
            resolve();
        });
    });
}

npmInstall();

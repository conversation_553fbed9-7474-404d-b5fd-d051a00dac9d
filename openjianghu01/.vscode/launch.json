{
    "version": "0.2.0",
    "configurations": [
		{ "name":"拉取项目", "command": "node .vscode/git-clone-pull.js","request":"launch","type":"node-terminal"},
		{ "name":"copy config", "command": "node .vscode/copy-config.js","request":"launch","type":"node-terminal"},
		{ "name":"copy config.env", "command": "node .vscode/copy-config.env.js","request":"launch","type":"node-terminal"},
		{ "name":"npm install", "command": "node .vscode/npm-install.js","request":"launch","type":"node-terminal"},
		{ "name":"generate-launch", "command": "node .vscode/generate-launch.js","request":"launch","type":"node-terminal"},
		{ "name":"execute-input", "command": "node .vscode/execute-input.js","request":"launch","type":"node-terminal"},
		// 注意: restart all; stop all; 必需在bash环境执行，node debug环境执行失败
		{ "name":"---------------------", "command":"---------------------","request":"launch","type":"node-terminal"},
		{ "name": "002.dev-scripts/update", "command": "npm --prefix=002.jianghu-init/dev-scripts run update", "request": "launch", "type": "node-terminal" },
		{ "name": "002.dev-scripts/serve-for-develop", "command": "npm --prefix=002.jianghu-init/dev-scripts run serve-for-develop", "request": "launch", "type": "node-terminal" },
		{ "name": "105.cn-openjianghu-admin-v2", "command": "npm --prefix=105.cn-openjianghu-admin-v2 run dev", "request": "launch", "type": "node-terminal" },
		{ "name": "106.cn-openjianghu-seo-v2", "command": "npm --prefix=106.cn-openjianghu-seo-v2 run dev", "request": "launch", "type": "node-terminal" },
		{ "name": "002.jianghu-init【js file test】", "command": "cd ./002.jianghu-init/temp/test01 && echo -e '\n\n当前目录: '`pwd` && node ../../jianghu-init/bin/jianghu-init.js json --jsFile=`pwd`/jianghu-init-json/studnetManagement1.js --registry=http://localhost:8811", "request": "launch", "type": "node-terminal" },
		{ "name":"------init-json-test---------------", "command":"---------------------","request":"launch","type":"node-terminal"},
		{ "name": "121.jianghu-init-json-test", "command": "npm --prefix=121.jianghu-init-json-test run dev", "request": "launch", "type": "node-terminal" },
		{ "name": "JS.121.jianghu-init-json-test", "command": "npm --prefix=JS.121.jianghu-init-json-test run dev", "request": "launch", "type": "node-terminal" },
		{ "name": "JS.205.jianghujs-basic", "command": "npm --prefix=JS.205.jianghujs-basic run dev", "request": "launch", "type": "node-terminal" },
		{ "name": "JS.220.jianghujs-advanced", "command": "npm --prefix=JS.220.jianghujs-advanced run dev", "request": "launch", "type": "node-terminal" },
		{ "name": "JS.303.jianghu-hr", "command": "npm --prefix=JS.303.jianghu-hr run dev", "request": "launch", "type": "node-terminal" },


	]
}

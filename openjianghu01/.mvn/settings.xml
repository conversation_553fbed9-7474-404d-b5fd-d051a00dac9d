<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <mirrors>
        <mirror>
            <id>aliyunmaven</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <mirror>
            <id>maven-central</id>
            <url>https://repo.maven.apache.org/maven2</url>
            <mirrorOf>!aliyunmaven</mirrorOf>
        </mirror>
        <mirror>
            <id>github</id>
            <url>https://repo.maven.apache.org/maven2</url>
            <mirrorOf>!aliyunmaven</mirrorOf>
        </mirror>
    </mirrors>
    <servers>
        <server>
            <id>central</id>
            <username>wjd+yPM2</username>
            <password>DFJMF19notBVJCY09sFFVdrND6Jis2lHBfl+y4s2APuu</password>
        </server>
    </servers>
</settings>
